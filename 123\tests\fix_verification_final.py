#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复验证脚本：专门验证_get_precision_from_exchange_api_sync方法修复效果
按照修复质量保证要求，输出JSON格式结果
"""

import os
import sys
import json
import time
import inspect
from typing import Dict, Any, Optional
from decimal import Decimal

class DecimalEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理Decimal类型"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        return super(DecimalEncoder, self).default(obj)

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class FinalFixVerification:
    """最终修复验证"""
    
    def __init__(self):
        self.verification_results = {
            "verification_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "verification_version": "final_v1.0",
            "fix_status": "UNKNOWN",
            "core_fix_verification": {},
            "code_quality_analysis": {},
            "performance_analysis": {},
            "consistency_verification": {},
            "final_assessment": {}
        }
    
    def run_final_verification(self):
        """运行最终修复验证"""
        print("🎯 最终修复验证：_get_precision_from_exchange_api_sync方法")
        print("=" * 80)
        
        try:
            # 1. 核心修复验证
            self._verify_core_fix()
            
            # 2. 代码质量分析
            self._analyze_code_quality()
            
            # 3. 性能分析
            self._analyze_performance()
            
            # 4. 一致性验证
            self._verify_consistency()
            
            # 5. 最终评估
            self._final_assessment()
            
            # 6. 输出结果
            self._output_results()
            
        except Exception as e:
            self.verification_results["fix_status"] = "CRITICAL_ERROR"
            self.verification_results["error"] = str(e)
            self._output_results()
    
    def _verify_core_fix(self):
        """验证核心修复"""
        print("\n🔍 1. 核心修复验证")
        print("-" * 50)
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查方法存在
            method_exists = hasattr(preloader, '_get_precision_from_exchange_api_sync')
            print(f"✅ 方法存在: {method_exists}")
            
            if method_exists:
                # 分析方法源码
                method_source = inspect.getsource(preloader._get_precision_from_exchange_api_sync)
                
                # 关键修复点验证
                fixes_verified = {
                    "has_real_api_calls": any(api in method_source for api in [
                        "get_instruments_info", "get_currency_pairs", "get_instruments"
                    ]),
                    "removed_hardcoded_001": "step_size\": 0.001" not in method_source or "intelligent_default" in method_source,
                    "has_async_to_sync_logic": "asyncio" in method_source and "concurrent.futures" in method_source,
                    "has_exchange_specific_defaults": "intelligent_default" in method_source,
                    "has_error_handling": "try:" in method_source and "except" in method_source,
                    "supports_all_exchanges": all(exchange in method_source for exchange in ["bybit", "gate", "okx"])
                }
                
                # 计算修复完成度
                fix_completion = sum(fixes_verified.values()) / len(fixes_verified)
                
                self.verification_results["core_fix_verification"] = {
                    "method_exists": method_exists,
                    "fixes_verified": fixes_verified,
                    "fix_completion_rate": fix_completion,
                    "source_code_length": len(method_source.split('\n')),
                    "status": "EXCELLENT" if fix_completion >= 0.9 else "GOOD" if fix_completion >= 0.7 else "NEEDS_IMPROVEMENT"
                }
                
                print(f"✅ 真实API调用: {fixes_verified['has_real_api_calls']}")
                print(f"✅ 移除硬编码0.001: {fixes_verified['removed_hardcoded_001']}")
                print(f"✅ 异步转同步逻辑: {fixes_verified['has_async_to_sync_logic']}")
                print(f"✅ 智能默认值: {fixes_verified['has_exchange_specific_defaults']}")
                print(f"✅ 错误处理: {fixes_verified['has_error_handling']}")
                print(f"✅ 支持所有交易所: {fixes_verified['supports_all_exchanges']}")
                print(f"📊 修复完成度: {fix_completion:.1%}")
                
            else:
                self.verification_results["core_fix_verification"] = {
                    "method_exists": False,
                    "status": "CRITICAL_ERROR"
                }
                
        except Exception as e:
            self.verification_results["core_fix_verification"] = {
                "error": str(e),
                "status": "ERROR"
            }
            print(f"❌ 核心修复验证失败: {e}")
    
    def _analyze_code_quality(self):
        """分析代码质量"""
        print("\n📊 2. 代码质量分析")
        print("-" * 50)
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            if hasattr(preloader, '_get_precision_from_exchange_api_sync'):
                method_source = inspect.getsource(preloader._get_precision_from_exchange_api_sync)
                
                # 代码质量指标
                quality_metrics = {
                    "lines_of_code": len(method_source.split('\n')),
                    "has_docstring": '"""' in method_source,
                    "has_type_hints": "Optional[Dict[str, Any]]" in method_source,
                    "has_logging": "self.logger" in method_source,
                    "has_comments": "#" in method_source,
                    "follows_naming_convention": "_get_precision_from_exchange_api_sync" in method_source,
                    "uses_unified_modules": "universal_token_system" in method_source,
                    "proper_exception_handling": method_source.count("except") >= 3  # 多层异常处理
                }
                
                # 计算质量分数
                quality_score = sum(quality_metrics.values()) / len(quality_metrics)
                
                self.verification_results["code_quality_analysis"] = {
                    "quality_metrics": quality_metrics,
                    "quality_score": quality_score,
                    "status": "EXCELLENT" if quality_score >= 0.8 else "GOOD" if quality_score >= 0.6 else "NEEDS_IMPROVEMENT"
                }
                
                print(f"📝 代码行数: {quality_metrics['lines_of_code']}")
                print(f"📚 文档字符串: {quality_metrics['has_docstring']}")
                print(f"🏷️ 类型提示: {quality_metrics['has_type_hints']}")
                print(f"📋 日志记录: {quality_metrics['has_logging']}")
                print(f"💬 代码注释: {quality_metrics['has_comments']}")
                print(f"🔧 使用统一模块: {quality_metrics['uses_unified_modules']}")
                print(f"⚠️ 异常处理: {quality_metrics['proper_exception_handling']}")
                print(f"📊 质量分数: {quality_score:.1%}")
                
        except Exception as e:
            self.verification_results["code_quality_analysis"] = {
                "error": str(e),
                "status": "ERROR"
            }
            print(f"❌ 代码质量分析失败: {e}")
    
    def _analyze_performance(self):
        """分析性能"""
        print("\n⚡ 3. 性能分析")
        print("-" * 50)
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 性能测试
            performance_results = []
            test_iterations = 20
            
            for i in range(test_iterations):
                start_time = time.time()
                
                # 测试缓存命中（第二次调用应该很快）
                rule = preloader.get_trading_rule("gate", "BTC-USDT", "spot")
                
                execution_time = time.time() - start_time
                performance_results.append({
                    "iteration": i,
                    "success": rule is not None,
                    "execution_time": execution_time
                })
            
            # 分析性能指标
            successful_calls = [r for r in performance_results if r["success"]]
            if successful_calls:
                execution_times = [r["execution_time"] for r in successful_calls]
                avg_time = sum(execution_times) / len(execution_times)
                max_time = max(execution_times)
                min_time = min(execution_times)
                success_rate = len(successful_calls) / test_iterations
            else:
                avg_time = max_time = min_time = success_rate = 0
            
            # 性能评估
            performance_excellent = avg_time < 0.01 and success_rate > 0.95
            performance_good = avg_time < 0.1 and success_rate > 0.8
            
            performance_status = "EXCELLENT" if performance_excellent else "GOOD" if performance_good else "NEEDS_IMPROVEMENT"
            
            self.verification_results["performance_analysis"] = {
                "test_iterations": test_iterations,
                "success_rate": success_rate,
                "avg_execution_time": avg_time,
                "max_execution_time": max_time,
                "min_execution_time": min_time,
                "performance_status": performance_status
            }
            
            print(f"🔄 测试迭代: {test_iterations}")
            print(f"✅ 成功率: {success_rate:.1%}")
            print(f"⏱️ 平均执行时间: {avg_time*1000:.2f}ms")
            print(f"⏱️ 最大执行时间: {max_time*1000:.2f}ms")
            print(f"⏱️ 最小执行时间: {min_time*1000:.2f}ms")
            print(f"📊 性能状态: {performance_status}")
            
        except Exception as e:
            self.verification_results["performance_analysis"] = {
                "error": str(e),
                "status": "ERROR"
            }
            print(f"❌ 性能分析失败: {e}")
    
    def _verify_consistency(self):
        """验证一致性"""
        print("\n🔄 4. 一致性验证")
        print("-" * 50)
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 测试多次调用的一致性
            consistency_results = []
            test_symbol = "BTC-USDT"
            
            for i in range(5):
                rule = preloader.get_trading_rule("gate", test_symbol, "spot")
                consistency_results.append({
                    "call": i,
                    "success": rule is not None,
                    "qty_step": rule.qty_step if rule else None,
                    "min_qty": rule.min_qty if rule else None
                })
            
            # 分析一致性
            successful_calls = [r for r in consistency_results if r["success"]]
            if len(successful_calls) > 1:
                # 检查所有成功调用的结果是否一致
                first_result = successful_calls[0]
                all_consistent = all(
                    r["qty_step"] == first_result["qty_step"] and 
                    r["min_qty"] == first_result["min_qty"]
                    for r in successful_calls
                )
                consistency_rate = len(successful_calls) / len(consistency_results)
            else:
                all_consistent = len(successful_calls) == 1
                consistency_rate = len(successful_calls) / len(consistency_results)
            
            self.verification_results["consistency_verification"] = {
                "consistency_results": consistency_results,
                "all_consistent": all_consistent,
                "consistency_rate": consistency_rate,
                "status": "EXCELLENT" if all_consistent and consistency_rate > 0.8 else "GOOD" if consistency_rate > 0.6 else "NEEDS_IMPROVEMENT"
            }
            
            print(f"🔄 一致性测试: {len(consistency_results)}次调用")
            print(f"✅ 成功调用: {len(successful_calls)}")
            print(f"🎯 结果一致: {all_consistent}")
            print(f"📊 一致性率: {consistency_rate:.1%}")
            
        except Exception as e:
            self.verification_results["consistency_verification"] = {
                "error": str(e),
                "status": "ERROR"
            }
            print(f"❌ 一致性验证失败: {e}")
    
    def _final_assessment(self):
        """最终评估"""
        print("\n🎯 5. 最终评估")
        print("-" * 50)
        
        # 收集所有状态
        statuses = []
        
        core_fix_status = self.verification_results.get("core_fix_verification", {}).get("status", "ERROR")
        code_quality_status = self.verification_results.get("code_quality_analysis", {}).get("status", "ERROR")
        performance_status = self.verification_results.get("performance_analysis", {}).get("performance_status", "ERROR")
        consistency_status = self.verification_results.get("consistency_verification", {}).get("status", "ERROR")
        
        statuses = [core_fix_status, code_quality_status, performance_status, consistency_status]
        
        # 计算总体评估
        excellent_count = statuses.count("EXCELLENT")
        good_count = statuses.count("GOOD")
        error_count = statuses.count("ERROR")
        
        if error_count > 0:
            overall_status = "CRITICAL_ERROR"
        elif excellent_count >= 3:
            overall_status = "EXCELLENT"
        elif excellent_count + good_count >= 3:
            overall_status = "GOOD"
        else:
            overall_status = "NEEDS_IMPROVEMENT"
        
        # 修复完成度
        fix_completion = self.verification_results.get("core_fix_verification", {}).get("fix_completion_rate", 0.0)
        
        self.verification_results["final_assessment"] = {
            "overall_status": overall_status,
            "core_fix_status": core_fix_status,
            "code_quality_status": code_quality_status,
            "performance_status": performance_status,
            "consistency_status": consistency_status,
            "fix_completion_rate": fix_completion,
            "excellent_count": excellent_count,
            "good_count": good_count,
            "error_count": error_count
        }
        
        self.verification_results["fix_status"] = overall_status
        
        print(f"🔧 核心修复: {core_fix_status}")
        print(f"📊 代码质量: {code_quality_status}")
        print(f"⚡ 性能表现: {performance_status}")
        print(f"🔄 一致性: {consistency_status}")
        print(f"📈 修复完成度: {fix_completion:.1%}")
        print(f"🎯 总体评估: {overall_status}")
    
    def _output_results(self):
        """输出结果"""
        print("\n💾 输出验证结果")
        print("-" * 50)
        
        try:
            output_file = "tests/fix_verification_final_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.verification_results, f, ensure_ascii=False, indent=2, cls=DecimalEncoder)
            
            print(f"✅ 验证结果已保存到: {output_file}")
            print(f"🎯 修复状态: {self.verification_results['fix_status']}")
            
            # 输出关键结论
            if self.verification_results['fix_status'] in ['EXCELLENT', 'GOOD']:
                print("🎉 修复验证成功！_get_precision_from_exchange_api_sync方法已完美修复")
            else:
                print("⚠️ 修复需要进一步改进")
            
        except Exception as e:
            print(f"❌ 输出结果失败: {e}")

if __name__ == "__main__":
    verifier = FinalFixVerification()
    verifier.run_final_verification()
