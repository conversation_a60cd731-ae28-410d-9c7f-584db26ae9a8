#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精准诊断脚本：_get_precision_from_exchange_api_sync方法问题分析
根据修复提示词要求，精准定位问题根源
"""

import os
import sys
import json
import time
import asyncio
from typing import Dict, Any, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class PrecisionAPIDiagnosis:
    """精准诊断：交易规则精度API调用问题"""
    
    def __init__(self):
        self.results = {
            "diagnosis_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "problem_analysis": {},
            "root_causes": {},
            "api_call_analysis": {},
            "cache_mechanism_analysis": {},
            "recommendations": []
        }
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("🔍 开始精准诊断：_get_precision_from_exchange_api_sync方法问题")
        print("=" * 60)
        
        # 1. 分析同步API方法实现
        self._analyze_sync_api_method()
        
        # 2. 分析异步API方法实现
        self._analyze_async_api_method()
        
        # 3. 分析缓存机制
        self._analyze_cache_mechanism()
        
        # 4. 分析全局交易所实例问题
        self._analyze_global_exchanges()
        
        # 5. 分析API调用链路
        self._analyze_api_call_chain()
        
        # 6. 生成修复建议
        self._generate_recommendations()
        
        # 7. 输出诊断结果
        self._output_results()
    
    def _analyze_sync_api_method(self):
        """分析同步API方法实现"""
        print("\n📋 1. 分析同步API方法实现")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查方法是否存在
            if hasattr(preloader, '_get_precision_from_exchange_api_sync'):
                print("✅ _get_precision_from_exchange_api_sync方法存在")
                
                # 分析方法实现
                import inspect
                method_source = inspect.getsource(preloader._get_precision_from_exchange_api_sync)
                
                # 检查是否硬编码返回默认值
                if "return {" in method_source and "step_size" in method_source:
                    print("⚠️ 发现问题：方法硬编码返回默认值")
                    self.results["problem_analysis"]["sync_method"] = "硬编码返回默认值，跳过API调用"
                    
                    # 分析默认值
                    if "0.001" in method_source:
                        print("❌ 发现错误默认值：0.001")
                        self.results["root_causes"]["wrong_default"] = "硬编码返回0.001默认值"
                    
                    # 检查是否有真实API调用
                    if "await" not in method_source and "get_instruments" not in method_source:
                        print("❌ 关键问题：方法完全跳过API调用")
                        self.results["root_causes"]["skip_api"] = "同步方法完全跳过API调用，直接返回硬编码值"
                
            else:
                print("❌ _get_precision_from_exchange_api_sync方法不存在")
                self.results["problem_analysis"]["sync_method"] = "方法不存在"
                
        except Exception as e:
            print(f"❌ 分析同步API方法失败: {e}")
            self.results["problem_analysis"]["sync_method"] = f"分析失败: {e}"
    
    def _analyze_async_api_method(self):
        """分析异步API方法实现"""
        print("\n📋 2. 分析异步API方法实现")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            if hasattr(preloader, '_get_precision_from_exchange_api'):
                print("✅ _get_precision_from_exchange_api异步方法存在")
                
                # 分析方法实现
                import inspect
                method_source = inspect.getsource(preloader._get_precision_from_exchange_api)
                
                # 检查是否有真实API调用
                api_calls = []
                if "get_instruments_info" in method_source:
                    api_calls.append("Bybit: get_instruments_info")
                if "get_currency_pairs" in method_source:
                    api_calls.append("Gate: get_currency_pairs")
                if "get_instruments" in method_source:
                    api_calls.append("OKX: get_instruments")
                
                if api_calls:
                    print(f"✅ 异步方法包含真实API调用: {', '.join(api_calls)}")
                    self.results["api_call_analysis"]["async_method"] = {
                        "has_real_api_calls": True,
                        "api_methods": api_calls
                    }
                else:
                    print("❌ 异步方法缺少真实API调用")
                    self.results["api_call_analysis"]["async_method"] = {
                        "has_real_api_calls": False,
                        "api_methods": []
                    }
                    
            else:
                print("❌ _get_precision_from_exchange_api异步方法不存在")
                self.results["api_call_analysis"]["async_method"] = "方法不存在"
                
        except Exception as e:
            print(f"❌ 分析异步API方法失败: {e}")
            self.results["api_call_analysis"]["async_method"] = f"分析失败: {e}"
    
    def _analyze_cache_mechanism(self):
        """分析缓存机制"""
        print("\n📋 3. 分析缓存机制")
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # 检查get_trading_rule方法
            if hasattr(preloader, 'get_trading_rule'):
                print("✅ get_trading_rule方法存在")
                
                import inspect
                method_source = inspect.getsource(preloader.get_trading_rule)
                
                # 检查缓存逻辑
                cache_features = []
                if "cache_key" in method_source:
                    cache_features.append("缓存键生成")
                if "trading_rules" in method_source and "cache_key in" in method_source:
                    cache_features.append("缓存命中检查")
                if "timestamp" in method_source and "ttl" in method_source.lower():
                    cache_features.append("TTL过期检查")
                if "_get_precision_from_exchange_api_sync" in method_source:
                    cache_features.append("缓存未命中时调用同步API")
                
                print(f"✅ 缓存机制特性: {', '.join(cache_features)}")
                self.results["cache_mechanism_analysis"] = {
                    "cache_features": cache_features,
                    "uses_sync_api_on_miss": "_get_precision_from_exchange_api_sync" in method_source
                }
                
                # 关键问题：缓存未命中时的处理
                if "_get_precision_from_exchange_api_sync" in method_source:
                    print("⚠️ 发现问题：缓存未命中时调用有问题的同步API方法")
                    self.results["root_causes"]["cache_fallback"] = "缓存未命中时调用有问题的同步API方法"
                
            else:
                print("❌ get_trading_rule方法不存在")
                self.results["cache_mechanism_analysis"] = "方法不存在"
                
        except Exception as e:
            print(f"❌ 分析缓存机制失败: {e}")
            self.results["cache_mechanism_analysis"] = f"分析失败: {e}"
    
    def _analyze_global_exchanges(self):
        """分析全局交易所实例问题"""
        print("\n📋 4. 分析全局交易所实例问题")
        
        try:
            # 检查全局交易所实例
            try:
                from core.trading_rules_preloader import get_global_exchanges
                global_exchanges = get_global_exchanges()
                
                if global_exchanges:
                    print(f"✅ 全局交易所实例存在: {list(global_exchanges.keys())}")
                    self.results["api_call_analysis"]["global_exchanges"] = {
                        "exists": True,
                        "exchanges": list(global_exchanges.keys())
                    }
                else:
                    print("❌ 全局交易所实例为None")
                    self.results["root_causes"]["no_global_exchanges"] = "get_global_exchanges()返回None"
                    
            except ImportError as ie:
                print(f"❌ 无法导入get_global_exchanges: {ie}")
                self.results["root_causes"]["import_error"] = f"无法导入get_global_exchanges: {ie}"
                
        except Exception as e:
            print(f"❌ 分析全局交易所实例失败: {e}")
            self.results["api_call_analysis"]["global_exchanges"] = f"分析失败: {e}"
    
    def _analyze_api_call_chain(self):
        """分析API调用链路"""
        print("\n📋 5. 分析API调用链路")
        
        # 分析调用链路：get_trading_rule -> 缓存检查 -> API调用
        call_chain = [
            "get_trading_rule(exchange, symbol, market_type)",
            "检查缓存: trading_rules[cache_key]",
            "缓存命中: 返回缓存结果",
            "缓存未命中: 调用_get_precision_from_exchange_api_sync",
            "同步API方法: 硬编码返回默认值(问题所在)"
        ]
        
        print("📊 API调用链路分析:")
        for i, step in enumerate(call_chain, 1):
            if "问题所在" in step:
                print(f"❌ {i}. {step}")
            else:
                print(f"✅ {i}. {step}")
        
        self.results["api_call_analysis"]["call_chain"] = call_chain
        
        # 正确的调用链路应该是
        correct_chain = [
            "get_trading_rule(exchange, symbol, market_type)",
            "检查缓存: trading_rules[cache_key]",
            "缓存命中: 返回缓存结果",
            "缓存未命中: 调用真实API获取精度信息",
            "API失败时: 使用基于交易所特性的默认值"
        ]
        
        print("\n📊 正确的API调用链路应该是:")
        for i, step in enumerate(correct_chain, 1):
            print(f"✅ {i}. {step}")
        
        self.results["api_call_analysis"]["correct_chain"] = correct_chain
    
    def _generate_recommendations(self):
        """生成修复建议"""
        print("\n📋 6. 生成修复建议")
        
        recommendations = [
            {
                "priority": "HIGH",
                "issue": "同步API方法硬编码返回错误默认值",
                "solution": "修复_get_precision_from_exchange_api_sync方法，实现真实API调用",
                "details": "方法应该调用交易所API获取真实精度信息，而不是硬编码返回0.001"
            },
            {
                "priority": "HIGH", 
                "issue": "缓存未命中时使用错误的回退机制",
                "solution": "优化缓存+API策略，API失败时使用基于交易所特性的默认值",
                "details": "当API调用失败时，应该使用不同交易所的特定默认值，而不是统一的0.001"
            },
            {
                "priority": "MEDIUM",
                "issue": "全局交易所实例可能未正确设置",
                "solution": "确保在系统初始化时正确调用set_global_exchanges",
                "details": "检查trading_system_initializer中是否正确设置全局交易所实例"
            },
            {
                "priority": "LOW",
                "issue": "需要统一三交易所的精度处理逻辑",
                "solution": "确保Gate.io、Bybit、OKX使用一致的精度获取和处理逻辑",
                "details": "所有交易所都应该受益于修复，确保系统完全一致"
            }
        ]
        
        for rec in recommendations:
            priority_icon = "🔥" if rec["priority"] == "HIGH" else "⚠️" if rec["priority"] == "MEDIUM" else "💡"
            print(f"{priority_icon} {rec['priority']}: {rec['issue']}")
            print(f"   解决方案: {rec['solution']}")
            print(f"   详细说明: {rec['details']}\n")
        
        self.results["recommendations"] = recommendations
    
    def _output_results(self):
        """输出诊断结果"""
        print("\n📊 诊断结果总结")
        print("=" * 60)
        
        # 输出到JSON文件
        output_file = "tests/precision_api_diagnosis_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 诊断结果已保存到: {output_file}")
        
        # 输出关键发现
        print("\n🎯 关键发现:")
        if "skip_api" in self.results["root_causes"]:
            print("❌ 核心问题: _get_precision_from_exchange_api_sync方法完全跳过API调用")
        if "wrong_default" in self.results["root_causes"]:
            print("❌ 错误默认值: 硬编码返回0.001，影响所有交易所")
        if "cache_fallback" in self.results["root_causes"]:
            print("❌ 缓存回退问题: 缓存未命中时使用有问题的同步API")
        
        print(f"\n📈 修复优先级: {len([r for r in self.results['recommendations'] if r['priority'] == 'HIGH'])}个高优先级问题需要立即修复")

if __name__ == "__main__":
    diagnosis = PrecisionAPIDiagnosis()
    diagnosis.run_diagnosis()
