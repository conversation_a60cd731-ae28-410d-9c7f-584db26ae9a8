#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机构级别高质量测试：_get_precision_from_exchange_api_sync方法修复验证
按照修复质量保证要求，进行三段进阶验证机制
"""

import os
import sys
import json
import time
import asyncio
import threading
from typing import Dict, Any, Optional, List
from decimal import Decimal

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class InstitutionalGradeFixValidation:
    """机构级别修复验证测试"""
    
    def __init__(self):
        self.test_results = {
            "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "test_version": "institutional_grade_v1.0",
            "overall_status": "UNKNOWN",
            "stage1_basic_core": {"status": "PENDING", "tests": [], "success_rate": 0.0},
            "stage2_system_integration": {"status": "PENDING", "tests": [], "success_rate": 0.0},
            "stage3_production_simulation": {"status": "PENDING", "tests": [], "success_rate": 0.0},
            "performance_metrics": {},
            "consistency_analysis": {},
            "error_analysis": [],
            "recommendations": []
        }
        
        # 测试交易对列表（覆盖多种场景）
        self.test_symbols = [
            "SPK-USDT",  # 原问题交易对
            "BTC-USDT",  # 主流交易对
            "ETH-USDT",  # 主流交易对
            "DOGE-USDT", # 小币种
            "NONEXIST-USDT"  # 不存在的交易对
        ]
        
        self.test_exchanges = ["gate", "bybit", "okx"]
        self.test_market_types = ["spot", "futures"]
    
    def run_full_validation(self):
        """运行完整的三段进阶验证"""
        print("🏛️ 开始机构级别高质量测试")
        print("=" * 80)
        
        try:
            # Stage 1: 基础核心测试
            self._stage1_basic_core_tests()
            
            # Stage 2: 复杂系统级联测试
            self._stage2_system_integration_tests()
            
            # Stage 3: 生产环境仿真测试
            self._stage3_production_simulation_tests()
            
            # 综合分析
            self._comprehensive_analysis()
            
            # 输出结果
            self._output_test_results()
            
        except Exception as e:
            self.test_results["overall_status"] = "CRITICAL_ERROR"
            self.test_results["error_analysis"].append({
                "type": "CRITICAL_TEST_FAILURE",
                "message": str(e),
                "timestamp": time.time()
            })
            self._output_test_results()
    
    def _stage1_basic_core_tests(self):
        """Stage 1: 基础核心测试 - 模块单元功能验证"""
        print("\n🔬 Stage 1: 基础核心测试")
        print("-" * 50)
        
        stage1_tests = []
        
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()
            
            # Test 1.1: 方法存在性验证
            test_1_1 = self._test_method_existence(preloader)
            stage1_tests.append(test_1_1)
            
            # Test 1.2: 参数输入输出验证
            test_1_2 = self._test_parameter_validation(preloader)
            stage1_tests.append(test_1_2)
            
            # Test 1.3: 边界条件检查
            test_1_3 = self._test_boundary_conditions(preloader)
            stage1_tests.append(test_1_3)
            
            # Test 1.4: 错误处理验证
            test_1_4 = self._test_error_handling(preloader)
            stage1_tests.append(test_1_4)
            
            # Test 1.5: API调用逻辑验证
            test_1_5 = self._test_api_call_logic(preloader)
            stage1_tests.append(test_1_5)
            
            # 计算Stage 1成功率
            success_count = sum(1 for test in stage1_tests if test["status"] == "PASS")
            success_rate = success_count / len(stage1_tests) if stage1_tests else 0.0
            
            self.test_results["stage1_basic_core"] = {
                "status": "PASS" if success_rate >= 1.0 else "FAIL",
                "tests": stage1_tests,
                "success_rate": success_rate,
                "total_tests": len(stage1_tests),
                "passed_tests": success_count
            }
            
            print(f"✅ Stage 1 完成: {success_count}/{len(stage1_tests)} 测试通过 ({success_rate:.1%})")
            
        except Exception as e:
            self.test_results["stage1_basic_core"]["status"] = "ERROR"
            self.test_results["error_analysis"].append({
                "stage": "stage1",
                "error": str(e),
                "timestamp": time.time()
            })
            print(f"❌ Stage 1 失败: {e}")
    
    def _test_method_existence(self, preloader) -> Dict[str, Any]:
        """测试方法存在性"""
        test_result = {
            "test_name": "方法存在性验证",
            "test_id": "1.1",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }
        
        start_time = time.time()
        try:
            # 检查同步方法
            sync_method_exists = hasattr(preloader, '_get_precision_from_exchange_api_sync')
            
            # 检查异步方法
            async_method_exists = hasattr(preloader, '_get_precision_from_exchange_api')
            
            # 检查缓存方法
            cache_method_exists = hasattr(preloader, 'get_trading_rule')
            
            test_result["details"] = {
                "sync_method_exists": sync_method_exists,
                "async_method_exists": async_method_exists,
                "cache_method_exists": cache_method_exists
            }
            
            if sync_method_exists and async_method_exists and cache_method_exists:
                test_result["status"] = "PASS"
                print("✅ 1.1 方法存在性验证: 通过")
            else:
                test_result["status"] = "FAIL"
                print("❌ 1.1 方法存在性验证: 失败")
                
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 1.1 方法存在性验证: 错误 - {e}")
        
        test_result["execution_time"] = time.time() - start_time
        return test_result
    
    def _test_parameter_validation(self, preloader) -> Dict[str, Any]:
        """测试参数输入输出验证"""
        test_result = {
            "test_name": "参数输入输出验证",
            "test_id": "1.2",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }
        
        start_time = time.time()
        try:
            # 创建模拟交易所实例
            from exchanges.gate_exchange import GateExchange
            temp_gate = GateExchange()
            
            # 测试正常参数
            result = preloader._get_precision_from_exchange_api_sync(
                temp_gate, "BTC-USDT", "spot"
            )
            
            test_result["details"] = {
                "normal_params_result": result is not None,
                "result_type": type(result).__name__,
                "has_required_fields": False
            }
            
            # 检查返回结果的必需字段
            if result and isinstance(result, dict):
                required_fields = ["step_size", "min_amount", "price_precision", "amount_precision"]
                has_all_fields = all(field in result for field in required_fields)
                test_result["details"]["has_required_fields"] = has_all_fields
                test_result["details"]["returned_fields"] = list(result.keys())
                
                if has_all_fields:
                    test_result["status"] = "PASS"
                    print("✅ 1.2 参数输入输出验证: 通过")
                else:
                    test_result["status"] = "FAIL"
                    print("❌ 1.2 参数输入输出验证: 缺少必需字段")
            else:
                test_result["status"] = "FAIL"
                print("❌ 1.2 参数输入输出验证: 返回结果无效")
                
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 1.2 参数输入输出验证: 错误 - {e}")
        
        test_result["execution_time"] = time.time() - start_time
        return test_result
    
    def _test_boundary_conditions(self, preloader) -> Dict[str, Any]:
        """测试边界条件"""
        test_result = {
            "test_name": "边界条件检查",
            "test_id": "1.3",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }
        
        start_time = time.time()
        try:
            from exchanges.gate_exchange import GateExchange
            temp_gate = GateExchange()
            
            boundary_tests = []
            
            # 测试None参数
            try:
                result = preloader._get_precision_from_exchange_api_sync(None, "BTC-USDT", "spot")
                boundary_tests.append({"test": "None_exchange", "passed": result is None})
            except:
                boundary_tests.append({"test": "None_exchange", "passed": True})  # 异常也是正确处理
            
            # 测试空字符串
            try:
                result = preloader._get_precision_from_exchange_api_sync(temp_gate, "", "spot")
                boundary_tests.append({"test": "empty_symbol", "passed": result is None})
            except:
                boundary_tests.append({"test": "empty_symbol", "passed": True})
            
            # 测试无效市场类型
            try:
                result = preloader._get_precision_from_exchange_api_sync(temp_gate, "BTC-USDT", "invalid")
                boundary_tests.append({"test": "invalid_market_type", "passed": True})
            except:
                boundary_tests.append({"test": "invalid_market_type", "passed": True})
            
            test_result["details"]["boundary_tests"] = boundary_tests
            passed_count = sum(1 for test in boundary_tests if test["passed"])
            
            if passed_count == len(boundary_tests):
                test_result["status"] = "PASS"
                print("✅ 1.3 边界条件检查: 通过")
            else:
                test_result["status"] = "FAIL"
                print("❌ 1.3 边界条件检查: 部分失败")
                
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 1.3 边界条件检查: 错误 - {e}")
        
        test_result["execution_time"] = time.time() - start_time
        return test_result
    
    def _test_error_handling(self, preloader) -> Dict[str, Any]:
        """测试错误处理"""
        test_result = {
            "test_name": "错误处理验证",
            "test_id": "1.4",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }
        
        start_time = time.time()
        try:
            # 测试不存在的交易对
            from exchanges.gate_exchange import GateExchange
            temp_gate = GateExchange()
            
            result = preloader._get_precision_from_exchange_api_sync(
                temp_gate, "NONEXIST-USDT", "spot"
            )
            
            # 不存在的交易对应该返回None或智能默认值
            error_handled_correctly = result is None or (isinstance(result, dict) and result.get("source") == "intelligent_default")
            
            test_result["details"] = {
                "nonexistent_pair_handled": error_handled_correctly,
                "result": result
            }
            
            if error_handled_correctly:
                test_result["status"] = "PASS"
                print("✅ 1.4 错误处理验证: 通过")
            else:
                test_result["status"] = "FAIL"
                print("❌ 1.4 错误处理验证: 失败")
                
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 1.4 错误处理验证: 错误 - {e}")
        
        test_result["execution_time"] = time.time() - start_time
        return test_result
    
    def _test_api_call_logic(self, preloader) -> Dict[str, Any]:
        """测试API调用逻辑"""
        test_result = {
            "test_name": "API调用逻辑验证",
            "test_id": "1.5",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }
        
        start_time = time.time()
        try:
            # 检查方法是否不再硬编码返回0.001
            import inspect
            method_source = inspect.getsource(preloader._get_precision_from_exchange_api_sync)
            
            # 检查是否包含真实API调用逻辑
            has_api_calls = any(api_method in method_source for api_method in [
                "get_instruments_info", "get_currency_pairs", "get_instruments"
            ])
            
            # 检查是否移除了硬编码的0.001
            no_hardcoded_001 = "step_size\": 0.001" not in method_source or "intelligent_default" in method_source
            
            # 检查是否有异步转同步逻辑
            has_async_to_sync = "asyncio" in method_source and "concurrent.futures" in method_source
            
            test_result["details"] = {
                "has_api_calls": has_api_calls,
                "no_hardcoded_001": no_hardcoded_001,
                "has_async_to_sync": has_async_to_sync
            }
            
            if has_api_calls and no_hardcoded_001:
                test_result["status"] = "PASS"
                print("✅ 1.5 API调用逻辑验证: 通过")
            else:
                test_result["status"] = "FAIL"
                print("❌ 1.5 API调用逻辑验证: 失败")
                
        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 1.5 API调用逻辑验证: 错误 - {e}")
        
        test_result["execution_time"] = time.time() - start_time
        return test_result

    def _stage2_system_integration_tests(self):
        """Stage 2: 复杂系统级联测试 - 模块交互逻辑验证"""
        print("\n🔗 Stage 2: 复杂系统级联测试")
        print("-" * 50)

        stage2_tests = []

        try:
            # Test 2.1: 多交易所一致性测试
            test_2_1 = self._test_multi_exchange_consistency()
            stage2_tests.append(test_2_1)

            # Test 2.2: 缓存与API联动测试
            test_2_2 = self._test_cache_api_integration()
            stage2_tests.append(test_2_2)

            # 计算Stage 2成功率
            success_count = sum(1 for test in stage2_tests if test["status"] == "PASS")
            success_rate = success_count / len(stage2_tests) if stage2_tests else 0.0

            self.test_results["stage2_system_integration"] = {
                "status": "PASS" if success_rate >= 0.8 else "FAIL",
                "tests": stage2_tests,
                "success_rate": success_rate,
                "total_tests": len(stage2_tests),
                "passed_tests": success_count
            }

            print(f"✅ Stage 2 完成: {success_count}/{len(stage2_tests)} 测试通过 ({success_rate:.1%})")

        except Exception as e:
            self.test_results["stage2_system_integration"]["status"] = "ERROR"
            self.test_results["error_analysis"].append({
                "stage": "stage2",
                "error": str(e),
                "timestamp": time.time()
            })
            print(f"❌ Stage 2 失败: {e}")

    def _test_multi_exchange_consistency(self) -> Dict[str, Any]:
        """测试多交易所一致性"""
        test_result = {
            "test_name": "多交易所一致性测试",
            "test_id": "2.1",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }

        start_time = time.time()
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()

            exchange_results = {}

            # 测试所有交易所的一致性
            for exchange_name in self.test_exchanges:
                try:
                    if exchange_name == "gate":
                        from exchanges.gate_exchange import GateExchange
                        exchange_instance = GateExchange()
                    elif exchange_name == "bybit":
                        from exchanges.bybit_exchange import BybitExchange
                        exchange_instance = BybitExchange()
                    elif exchange_name == "okx":
                        from exchanges.okx_exchange import OKXExchange
                        exchange_instance = OKXExchange()
                    else:
                        continue

                    # 测试主流交易对
                    result = preloader._get_precision_from_exchange_api_sync(
                        exchange_instance, "BTC-USDT", "spot"
                    )

                    exchange_results[exchange_name] = {
                        "success": result is not None,
                        "has_step_size": result and "step_size" in result,
                        "step_size": result.get("step_size") if result else None,
                        "source": result.get("source") if result else None
                    }

                except Exception as e:
                    exchange_results[exchange_name] = {
                        "success": False,
                        "error": str(e)
                    }

            # 分析一致性
            successful_exchanges = [name for name, result in exchange_results.items() if result.get("success")]
            consistency_score = len(successful_exchanges) / len(self.test_exchanges)

            test_result["details"] = {
                "exchange_results": exchange_results,
                "successful_exchanges": successful_exchanges,
                "consistency_score": consistency_score
            }

            if consistency_score >= 0.6:  # 至少60%的交易所成功（考虑到可能的网络问题）
                test_result["status"] = "PASS"
                print(f"✅ 2.1 多交易所一致性测试: 通过 ({consistency_score:.1%})")
            else:
                test_result["status"] = "FAIL"
                print(f"❌ 2.1 多交易所一致性测试: 失败 ({consistency_score:.1%})")

        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 2.1 多交易所一致性测试: 错误 - {e}")

        test_result["execution_time"] = time.time() - start_time
        return test_result

    def _test_cache_api_integration(self) -> Dict[str, Any]:
        """测试缓存与API联动"""
        test_result = {
            "test_name": "缓存与API联动测试",
            "test_id": "2.2",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }

        start_time = time.time()
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()

            # 清空缓存
            preloader.trading_rules.clear()

            # 第一次调用（应该触发API调用）
            first_call_start = time.time()
            rule1 = preloader.get_trading_rule("gate", "BTC-USDT", "spot")
            first_call_time = time.time() - first_call_start

            # 第二次调用（应该命中缓存）
            second_call_start = time.time()
            rule2 = preloader.get_trading_rule("gate", "BTC-USDT", "spot")
            second_call_time = time.time() - second_call_start

            # 分析结果
            both_successful = rule1 is not None and rule2 is not None
            cache_faster = second_call_time < first_call_time * 0.8  # 缓存应该更快

            test_result["details"] = {
                "first_call_success": rule1 is not None,
                "second_call_success": rule2 is not None,
                "first_call_time": first_call_time,
                "second_call_time": second_call_time,
                "cache_faster": cache_faster,
                "cache_size": len(preloader.trading_rules)
            }

            if both_successful:
                test_result["status"] = "PASS"
                print(f"✅ 2.2 缓存与API联动测试: 通过")
            else:
                test_result["status"] = "FAIL"
                print(f"❌ 2.2 缓存与API联动测试: 失败")

        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 2.2 缓存与API联动测试: 错误 - {e}")

        test_result["execution_time"] = time.time() - start_time
        return test_result

    def _stage3_production_simulation_tests(self):
        """Stage 3: 生产环境仿真测试"""
        print("\n🏭 Stage 3: 生产环境仿真测试")
        print("-" * 50)

        stage3_tests = []

        try:
            # Test 3.1: 性能压力测试
            test_3_1 = self._test_performance_stress()
            stage3_tests.append(test_3_1)

            # Test 3.2: 并发安全测试
            test_3_2 = self._test_concurrent_safety()
            stage3_tests.append(test_3_2)

            # 计算Stage 3成功率
            success_count = sum(1 for test in stage3_tests if test["status"] == "PASS")
            success_rate = success_count / len(stage3_tests) if stage3_tests else 0.0

            self.test_results["stage3_production_simulation"] = {
                "status": "PASS" if success_rate >= 0.8 else "FAIL",
                "tests": stage3_tests,
                "success_rate": success_rate,
                "total_tests": len(stage3_tests),
                "passed_tests": success_count
            }

            print(f"✅ Stage 3 完成: {success_count}/{len(stage3_tests)} 测试通过 ({success_rate:.1%})")

        except Exception as e:
            self.test_results["stage3_production_simulation"]["status"] = "ERROR"
            self.test_results["error_analysis"].append({
                "stage": "stage3",
                "error": str(e),
                "timestamp": time.time()
            })
            print(f"❌ Stage 3 失败: {e}")

    def _test_performance_stress(self) -> Dict[str, Any]:
        """测试性能压力"""
        test_result = {
            "test_name": "性能压力测试",
            "test_id": "3.1",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }

        start_time = time.time()
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            preloader = get_trading_rules_preloader()

            # 性能测试参数
            test_iterations = 50
            performance_results = []

            # 执行多次调用测试性能
            for i in range(test_iterations):
                call_start = time.time()
                rule = preloader.get_trading_rule("gate", "BTC-USDT", "spot")
                call_time = time.time() - call_start

                performance_results.append({
                    "iteration": i,
                    "success": rule is not None,
                    "call_time": call_time
                })

            # 分析性能指标
            successful_calls = [r for r in performance_results if r["success"]]
            success_rate = len(successful_calls) / test_iterations

            if successful_calls:
                call_times = [r["call_time"] for r in successful_calls]
                avg_time = sum(call_times) / len(call_times)
                max_time = max(call_times)
                min_time = min(call_times)
            else:
                avg_time = max_time = min_time = 0

            # 性能要求：平均响应时间 < 100ms，成功率 > 90%
            performance_acceptable = avg_time < 0.1 and success_rate > 0.9

            test_result["details"] = {
                "test_iterations": test_iterations,
                "success_rate": success_rate,
                "avg_response_time": avg_time,
                "max_response_time": max_time,
                "min_response_time": min_time,
                "performance_acceptable": performance_acceptable
            }

            if performance_acceptable:
                test_result["status"] = "PASS"
                print(f"✅ 3.1 性能压力测试: 通过 (平均{avg_time*1000:.1f}ms, 成功率{success_rate:.1%})")
            else:
                test_result["status"] = "FAIL"
                print(f"❌ 3.1 性能压力测试: 失败 (平均{avg_time*1000:.1f}ms, 成功率{success_rate:.1%})")

        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 3.1 性能压力测试: 错误 - {e}")

        test_result["execution_time"] = time.time() - start_time
        return test_result

    def _test_concurrent_safety(self) -> Dict[str, Any]:
        """测试并发安全"""
        test_result = {
            "test_name": "并发安全测试",
            "test_id": "3.2",
            "status": "UNKNOWN",
            "details": {},
            "execution_time": 0.0
        }

        start_time = time.time()
        try:
            from core.trading_rules_preloader import get_trading_rules_preloader
            import threading

            preloader = get_trading_rules_preloader()
            results = []
            errors = []

            def worker_thread(thread_id):
                try:
                    for i in range(10):
                        rule = preloader.get_trading_rule("gate", f"BTC-USDT", "spot")
                        results.append({
                            "thread_id": thread_id,
                            "iteration": i,
                            "success": rule is not None
                        })
                except Exception as e:
                    errors.append({
                        "thread_id": thread_id,
                        "error": str(e)
                    })

            # 创建多个线程并发执行
            threads = []
            thread_count = 5

            for i in range(thread_count):
                thread = threading.Thread(target=worker_thread, args=(i,))
                threads.append(thread)
                thread.start()

            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=10)

            # 分析并发安全性
            total_operations = len(results)
            successful_operations = len([r for r in results if r["success"]])
            concurrent_success_rate = successful_operations / total_operations if total_operations > 0 else 0
            no_errors = len(errors) == 0

            test_result["details"] = {
                "thread_count": thread_count,
                "total_operations": total_operations,
                "successful_operations": successful_operations,
                "concurrent_success_rate": concurrent_success_rate,
                "errors": errors,
                "no_errors": no_errors
            }

            if concurrent_success_rate > 0.8 and no_errors:
                test_result["status"] = "PASS"
                print(f"✅ 3.2 并发安全测试: 通过 (成功率{concurrent_success_rate:.1%})")
            else:
                test_result["status"] = "FAIL"
                print(f"❌ 3.2 并发安全测试: 失败 (成功率{concurrent_success_rate:.1%}, 错误{len(errors)}个)")

        except Exception as e:
            test_result["status"] = "ERROR"
            test_result["details"]["error"] = str(e)
            print(f"❌ 3.2 并发安全测试: 错误 - {e}")

        test_result["execution_time"] = time.time() - start_time
        return test_result

    def _comprehensive_analysis(self):
        """综合分析"""
        print("\n📊 综合分析")
        print("-" * 50)

        # 计算总体成功率
        all_tests = []
        all_tests.extend(self.test_results["stage1_basic_core"].get("tests", []))
        all_tests.extend(self.test_results["stage2_system_integration"].get("tests", []))
        all_tests.extend(self.test_results["stage3_production_simulation"].get("tests", []))

        total_tests = len(all_tests)
        passed_tests = len([t for t in all_tests if t.get("status") == "PASS"])
        overall_success_rate = passed_tests / total_tests if total_tests > 0 else 0.0

        # 确定总体状态
        if overall_success_rate >= 0.95:
            overall_status = "EXCELLENT"
        elif overall_success_rate >= 0.85:
            overall_status = "GOOD"
        elif overall_success_rate >= 0.70:
            overall_status = "ACCEPTABLE"
        else:
            overall_status = "NEEDS_IMPROVEMENT"

        self.test_results["overall_status"] = overall_status
        self.test_results["performance_metrics"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "overall_success_rate": overall_success_rate,
            "stage1_success_rate": self.test_results["stage1_basic_core"].get("success_rate", 0.0),
            "stage2_success_rate": self.test_results["stage2_system_integration"].get("success_rate", 0.0),
            "stage3_success_rate": self.test_results["stage3_production_simulation"].get("success_rate", 0.0)
        }

        print(f"📈 总体测试结果: {overall_status}")
        print(f"📊 总体成功率: {overall_success_rate:.1%} ({passed_tests}/{total_tests})")
        print(f"🔬 Stage 1 成功率: {self.test_results['stage1_basic_core'].get('success_rate', 0.0):.1%}")
        print(f"🔗 Stage 2 成功率: {self.test_results['stage2_system_integration'].get('success_rate', 0.0):.1%}")
        print(f"🏭 Stage 3 成功率: {self.test_results['stage3_production_simulation'].get('success_rate', 0.0):.1%}")

    def _output_test_results(self):
        """输出测试结果到JSON文件"""
        print("\n💾 保存测试结果")
        print("-" * 50)

        try:
            output_file = "tests/institutional_grade_fix_validation_results.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, ensure_ascii=False, indent=2)

            print(f"✅ 测试结果已保存到: {output_file}")
            print(f"🎯 总体状态: {self.test_results['overall_status']}")

            # 输出关键指标
            metrics = self.test_results.get("performance_metrics", {})
            if metrics:
                print(f"📊 总体成功率: {metrics.get('overall_success_rate', 0.0):.1%}")
                print(f"📈 通过测试: {metrics.get('passed_tests', 0)}/{metrics.get('total_tests', 0)}")

        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")

if __name__ == "__main__":
    validator = InstitutionalGradeFixValidation()
    validator.run_full_validation()
